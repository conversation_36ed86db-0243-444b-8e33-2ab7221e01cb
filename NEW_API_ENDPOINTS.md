# New API Endpoints Documentation

This document describes the 12 new API endpoints that have been added to the OpenEngage Core API.

## Authentication
All endpoints require authentication using the `X-API-Token` header:
```
X-API-Token: default-token-replace-in-production
```

## HTML Email Generator Endpoints

### 1. POST /api/convert-text-to-html
Converts plain text email content to HTML format with brand styling.

**Request Body:**
```json
{
  "email_content": {
    "subject": "Email Subject",
    "content": "Plain text email content"
  },
  "product_url": "https://example.com/product",
  "product_name": "Product Name",
  "communication_settings": {},
  "recipient_email": "<EMAIL>",
  "recipient_first_name": "<PERSON>",
  "brand_guidelines": {},
  "template_name": "template_name"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "html_content": "HTML formatted email content"
  },
  "message": "<PERSON><PERSON> converted to HTML successfully"
}
```

### 2. POST /api/create-html-email
Creates a complete HTML email from plain text content.

**Request Body:**
```json
{
  "text_content": "Plain text email content",
  "subject": "Email Subject",
  "product_url": "https://example.com/product",
  "product_name": "Product Name",
  "sender_name": "OpenEngage Team",
  "recipient_email": "<EMAIL>",
  "recipient_first_name": "John",
  "organization_url": "https://company.com",
  "template_name": "template_name",
  "utm_params": {}
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "html_email": "Complete HTML email"
  },
  "message": "HTML email created successfully"
}
```

## Email Sender Endpoints

### 3. POST /api/send-test-email
Sends a test email using the configured email service provider.

**Request Body:**
```json
{
  "recipient_email": "<EMAIL>",
  "subject": "Test Email Subject",
  "html_content": "<html>Email content</html>",
  "provider": "SparkPost"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "results": {},
    "provider": "SparkPost"
  },
  "message": "Test email sent successfully"
}
```

### 4. POST /api/send-email-campaign
Sends an email campaign to multiple recipients.

**Request Body:**
```json
{
  "email_data": [
    {
      "user_email": "<EMAIL>",
      "Subject": "Campaign Subject",
      "HTML_Content": "<html>Email content</html>"
    }
  ],
  "provider": "SparkPost"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "campaign_results": {},
    "provider": "SparkPost",
    "total_emails": 100
  },
  "message": "Email campaign sent successfully"
}
```

## Popup Content Generator Endpoints

### 5. POST /api/generate-popup-content
Generates personalized popup content for a single user.

**Request Body:**
```json
{
  "first_name": "John",
  "user_behavior": "visited product page",
  "target_product": "Analytics Course",
  "user_stage": "consideration",
  "api_key": "optional_openai_key"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "popup_content": {
      "popup_title": "Generated title",
      "popup_text": "Generated content",
      "button_text": "CTA text"
    }
  },
  "message": "Popup content generated successfully"
}
```

### 6. POST /api/generate-popup-batch
Generates popup content for multiple users in batch.

**Request Body:**
```json
{
  "user_data": [
    {
      "first_name": "John",
      "user_behavior": "visited product page",
      "target_product": "Analytics Course",
      "user_stage": "consideration"
    }
  ],
  "max_workers": 5,
  "batch_size": 50,
  "api_key": "optional_openai_key"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "generated_content": [],
    "total_processed": 100
  },
  "message": "Popup content generated for 100 users"
}
```

## Popup Configuration Manager Endpoints

### 7. POST /api/create-popup-config
Creates and saves a new popup configuration.

**Request Body:**
```json
{
  "config_data": {
    "popup_title": "Welcome",
    "popup_text": "Welcome message",
    "popup_button_text": "Get Started",
    "popup_delay_seconds": 3,
    "popup_type": "banner"
  },
  "config_file": "popupdata.json"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "config_file": "popupdata.json",
    "message": "Popup configuration saved successfully"
  },
  "message": "Popup configuration saved successfully"
}
```

### 8. POST /api/load-popup-config
Loads popup configuration from file.

**Request Body:**
```json
{
  "config_file": "popupdata.json"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "configuration": {},
    "config_file": "popupdata.json"
  },
  "message": "Popup configuration loaded successfully"
}
```

### 9. POST /api/update-popup-config
Updates specific fields in popup configuration.

**Request Body:**
```json
{
  "updates": {
    "popup_title": "New Title",
    "popup_delay_seconds": 5
  },
  "config_file": "popupdata.json"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "updated_fields": ["popup_title", "popup_delay_seconds"],
    "message": "Popup configuration updated successfully"
  },
  "message": "Popup configuration updated successfully"
}
```

## WhatsApp Sender Endpoints

### 10. POST /api/send-test-whatsapp
Sends a test WhatsApp message using a template.

**Request Body:**
```json
{
  "recipient_phone": "+**********",
  "selected_template_id": "template_123",
  "variable_values": ["John", "Analytics Course"],
  "selected_esp": "Gupshup"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "message_id": "msg_123",
    "status": "sent"
  },
  "message": "Test WhatsApp message sent successfully"
}
```

### 11. POST /api/send-whatsapp-campaign
Sends a WhatsApp campaign to multiple recipients.

**Request Body:**
```json
{
  "campaign_data": [
    {
      "phone_number": "+**********",
      "template_id": "template_123",
      "param_1": "John",
      "param_2": "Analytics Course"
    }
  ],
  "provider": "Gupshup"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "results": {},
    "campaign_data": []
  },
  "message": "WhatsApp campaign sent successfully"
}
```

## WhatsApp Template Fetcher Endpoints

### 12. POST /api/fetch-whatsapp-templates
Fetches templates from Gupshup and stores them locally.

**Request Body:**
```json
{
  "api_key": "gupshup_api_key",
  "app_id": "gupshup_app_id"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "fetched_count": 10,
    "stored_count": 10,
    "templates": []
  },
  "message": "WhatsApp templates fetched and stored successfully"
}
```

### 13. POST /api/get-whatsapp-templates
Retrieves all stored WhatsApp templates.

**Request Body:**
```json
{}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "templates": {},
    "product_mappings": {},
    "count": 10
  },
  "message": "WhatsApp templates retrieved successfully"
}
```

### 14. POST /api/create-template-mapping
Creates a mapping between a product and a WhatsApp template.

**Request Body:**
```json
{
  "product_name": "Analytics Course",
  "template_id": "template_123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "product_name": "Analytics Course",
    "template_id": "template_123",
    "message": "Successfully mapped Analytics Course to template template_123"
  },
  "message": "Successfully mapped Analytics Course to template template_123"
}
```

## Testing

All endpoints have been tested and are working correctly. You can use the provided `test_new_apis.py` script to verify functionality.
