#!/usr/bin/env python3
"""
Test script to verify that the new API endpoints work correctly.
"""

import requests
import json

BASE_URL = "http://localhost:5001/api"
TOKEN = "default-token-replace-in-production"
HEADERS = {
    "Content-Type": "application/json",
    "X-API-Token": TOKEN
}

def test_endpoint(endpoint, method="POST", data=None):
    """Test an API endpoint and return the result."""
    url = f"{BASE_URL}/{endpoint}"
    
    try:
        if method == "POST":
            response = requests.post(url, headers=HEADERS, json=data or {})
        else:
            response = requests.get(url, headers=HEADERS)
        
        return {
            "endpoint": endpoint,
            "status_code": response.status_code,
            "success": response.status_code == 200,
            "response": response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text
        }
    except Exception as e:
        return {
            "endpoint": endpoint,
            "status_code": None,
            "success": False,
            "error": str(e)
        }

def main():
    """Test all new API endpoints."""
    print("🧪 Testing New API Endpoints")
    print("=" * 60)
    
    # Test cases for each new endpoint
    test_cases = [
        {
            "endpoint": "convert-text-to-html",
            "data": {
                "email_content": {
                    "subject": "Test Email",
                    "content": "Hello, this is a test email content."
                },
                "product_name": "Test Product"
            }
        },
        {
            "endpoint": "create-html-email",
            "data": {
                "text_content": "Hello, this is a test email.",
                "subject": "Test Email"
            }
        },
        {
            "endpoint": "generate-popup-content",
            "data": {
                "first_name": "John",
                "user_behavior": "visited product page",
                "target_product": "Analytics Course",
                "user_stage": "consideration"
            }
        },
        {
            "endpoint": "load-popup-config",
            "data": {}
        },
        {
            "endpoint": "create-popup-config",
            "data": {
                "config_data": {
                    "popup_title": "Test Popup",
                    "popup_text": "This is a test popup",
                    "popup_delay_seconds": 2
                }
            }
        },
        {
            "endpoint": "get-whatsapp-templates",
            "data": {}
        },
        {
            "endpoint": "fetch-whatsapp-templates",
            "data": {}
        }
    ]
    
    results = []
    
    for test_case in test_cases:
        print(f"Testing {test_case['endpoint']}...")
        result = test_endpoint(test_case['endpoint'], data=test_case['data'])
        results.append(result)
        
        if result['success']:
            print(f"  ✅ {test_case['endpoint']} - SUCCESS")
        else:
            print(f"  ❌ {test_case['endpoint']} - FAILED (Status: {result.get('status_code', 'N/A')})")
            if 'error' in result:
                print(f"     Error: {result['error']}")
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    successful = sum(1 for r in results if r['success'])
    total = len(results)
    
    print(f"✅ Successful: {successful}/{total}")
    print(f"❌ Failed: {total - successful}/{total}")
    
    if successful == total:
        print("\n🎉 ALL NEW API ENDPOINTS ARE WORKING!")
    else:
        print(f"\n⚠️  {total - successful} endpoint(s) need attention.")
        
        print("\nFailed endpoints:")
        for result in results:
            if not result['success']:
                print(f"  - {result['endpoint']}")

if __name__ == "__main__":
    main()
